import { <PERSON> } from "@heroui/link";
import { But<PERSON> } from "@heroui/button";
import { Card, CardBody } from "@heroui/card";
import { motion } from "framer-motion";

import { HuaweiAppGalleryIcon } from "@/components/huawei-icon";

// App Store and Play Store Icons
const AppStoreIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
  </svg>
);

const PlayStoreIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.92 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
  </svg>
);



// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.6,
      staggerChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

const phoneVariants = {
  hidden: { opacity: 0, x: -50, rotateY: -15 },
  visible: {
    opacity: 1,
    x: 0,
    rotateY: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  }
};

export default function IndexPage() {
  return (
    <div className="relative flex flex-col h-screen bg-white">
      <motion.main 
        className="flex-1 flex items-center justify-center px-6 py-12"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Hero Section - Two Column Layout */}
        <div className="w-full max-w-6xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 items-center">
            {/* Left Side - App Images */}
            <motion.div 
              className="flex justify-center lg:justify-end"
              variants={phoneVariants}
            >
              <div className="relative">
                {/* Main Phone Mockup */}
                <motion.div 
                  className="relative z-10"
                  whileHover={{ 
                    scale: 1.02,
                    rotateY: 5,
                    transition: { duration: 0.3 }
                  }}
                >
                  <Card className="w-72 h-[580px] bg-gray-100 shadow-xl border border-gray-200 rounded-3xl overflow-hidden">
                    <CardBody className="p-4 flex flex-col">
                      {/* Phone Header */}
                      <motion.div 
                        className="flex items-center justify-between mb-4 bg-secondary-500 rounded-2xl p-4"
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.8, duration: 0.5 }}
                      >
                        <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                          <span className="text-secondary-500 text-base font-bold">LM</span>
                        </div>
                        <h3 className="text-white font-semibold text-lg">LuckyMall</h3>
                        <div className="w-8 h-8 bg-white/20 rounded-full"></div>
                      </motion.div>
                      
                      {/* App Screenshot Placeholder */}
                      <motion.div 
                        className="flex-1 bg-gray-100 rounded-2xl overflow-hidden"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 1, duration: 0.5 }}
                      >
                        <img 
                          src="https://picsum.photos/600/800?random=app"
                          alt="LuckyMall App Screenshot"
                          className="w-full h-full object-cover"
                        />
                      </motion.div>
                    </CardBody>
                  </Card>
                </motion.div>
              </div>
            </motion.div>

            {/* Right Side - Description and Buttons */}
            <motion.div 
              className="flex flex-col space-y-6 lg:pl-12"
              variants={itemVariants}
            >
              <motion.div variants={itemVariants}>
                <motion.h1 
                  className="text-4xl lg:text-5xl font-bold mb-4 text-gray-900 leading-tight"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.6 }}
                >
                  Welcome to{" "}
                  <motion.span 
                    className="text-secondary-500"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.8, duration: 0.6 }}
                  >
                    LuckyMall
                  </motion.span>
                </motion.h1>
                <motion.h2 
                  className="text-xl lg:text-2xl font-medium mb-6 text-warning-600 leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.6 }}
                >
                  Your Ultimate Shopping Companion
                </motion.h2>
                <motion.p 
                  className="text-lg leading-relaxed text-gray-600 mb-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.6 }}
                >
                  Discover amazing deals, exclusive offers, and a seamless shopping experience 
                  right at your fingertips. Download LuckyMall today and unlock a world of savings!
                </motion.p>
              </motion.div>

              {/* Features */}
              <motion.div 
                className="grid grid-cols-2 gap-4 mb-6"
                variants={itemVariants}
              >
                {[
                  { icon: "🚀", title: "Fast Delivery", desc: "Same day shipping", color: "warning" },
                  { icon: "🔒", title: "Secure Payment", desc: "256-bit encryption", color: "secondary" }
                ].map((feature, i) => (
                  <motion.div 
                    key={i}
                    className="flex items-center space-x-3"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.9 + (i * 0.1), duration: 0.5 }}
                    whileHover={{ 
                      scale: 1.02,
                      transition: { duration: 0.2 }
                    }}
                  >
                    <motion.div 
                      className={`w-10 h-10 bg-${feature.color}-100 rounded-lg flex items-center justify-center`}
                      whileHover={{ 
                        scale: 1.1,
                        rotate: 5,
                        transition: { duration: 0.2 }
                      }}
                    >
                      <span className={`text-${feature.color}-600 text-lg`}>{feature.icon}</span>
                    </motion.div>
                    <div>
                      <div className="font-medium text-gray-900">{feature.title}</div>
                      <div className="text-sm text-gray-500">{feature.desc}</div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>

              {/* Download Buttons */}
              <motion.div 
                className="flex flex-col sm:flex-row gap-4 sm:gap-6 sm:justify-center"
                variants={itemVariants}
              >
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2, duration: 0.5 }}
                >
                  <Button
                    as={Link}
                    href="#"
                    size="lg"
                    color="default"
                    variant="solid"
                    className="px-4 py-4 font-medium w-full sm:w-auto h-14"
                    startContent={<AppStoreIcon />}
                  >
                    Download on App Store
                  </Button>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.4, duration: 0.5 }}
                >
                  <Button
                    as={Link}
                    href="#"
                    size="lg"
                    color="success"
                    variant="solid"
                    className="px-4 py-4 font-medium w-full sm:w-auto h-14"
                    startContent={<PlayStoreIcon />}
                  >
                    Get it on Google Play
                  </Button>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.6, duration: 0.5 }}
                >
                  <Button
                    as={Link}
                    href="#"
                    size="lg"
                    color="danger"
                    variant="solid"
                    className="px-4 py-4 font-medium w-full sm:w-auto h-14"
                    startContent={<HuaweiAppGalleryIcon />}
                  >
                    Explore on AppGallery
                  </Button>
                </motion.div>
              </motion.div>

              {/* Statistics */}
              <motion.div 
                className="pt-6 border-t border-gray-100"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.6, duration: 0.6 }}
              >
                <motion.p 
                  className="text-lg text-gray-600 mb-4 font-medium"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.8, duration: 0.5 }}
                >
                  Trusted by over <span className="font-semibold text-gray-900">1 million</span> customers worldwide
                </motion.p>
                <motion.div 
                  className="flex items-center space-x-6 text-sm text-gray-500"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 2, duration: 0.5 }}
                >
                  {[
                    { icon: "⭐", text: "4.8 App Store Rating", color: "text-yellow-500" },
                    { icon: "📱", text: "500K+ Downloads", color: "text-green-500" },
                    { icon: "🛍️", text: "1M+ Products", color: "text-blue-500" }
                  ].map((stat, i) => (
                    <motion.div 
                      key={i}
                      className="flex items-center space-x-2"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 2.2 + (i * 0.1), duration: 0.4 }}
                    >
                      <span className={stat.color}>{stat.icon}</span>
                      <span>{stat.text}</span>
                    </motion.div>
                  ))}
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </motion.main>
        </div>
  );
}
