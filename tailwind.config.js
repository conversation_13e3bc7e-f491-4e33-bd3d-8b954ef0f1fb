import {heroui} from "@heroui/theme"

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    './src/layouts/**/*.{js,ts,jsx,tsx,mdx}',
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  darkMode: "class",
  plugins: [heroui({
    themes: {
      light: {
        colors: {
          background: "#FFFFFF",
          foreground: "#11181C",
          primary: {
            50: "#fefefe",
            100: "#fdfdfd",
            200: "#fbfbfb",
            300: "#f8f8f8",
            400: "#f4f4f4",
            500: "#ffffff", // Main white
            600: "#e6e6e6",
            700: "#cccccc",
            800: "#b3b3b3",
            900: "#999999",
            DEFAULT: "#ffffff",
            foreground: "#000000",
          },
          secondary: {
            50: "#fef2f2",
            100: "#fee2e2",
            200: "#fecaca",
            300: "#fca5a5",
            400: "#f87171",
            500: "#ef4444", // Main red
            600: "#dc2626",
            700: "#b91c1c",
            800: "#991b1b",
            900: "#7f1d1d",
            DEFAULT: "#ef4444",
            foreground: "#ffffff",
          },
          warning: {
            50: "#fffbeb",
            100: "#fef3c7",
            200: "#fde68a",
            300: "#fcd34d",
            400: "#fbbf24",
            500: "#f59e0b", // Yellowish accent
            600: "#d97706",
            700: "#b45309",
            800: "#92400e",
            900: "#78350f",
            DEFAULT: "#f59e0b",
            foreground: "#000000",
          },
          success: {
            50: "#f0fdf4",
            100: "#dcfce7",
            200: "#bbf7d0",
            300: "#86efac",
            400: "#4ade80",
            500: "#22c55e", // Green for Play Store
            600: "#16a34a",
            700: "#15803d",
            800: "#166534",
            900: "#14532d",
            DEFAULT: "#22c55e",
            foreground: "#ffffff",
          },
          default: {
            50: "#eff6ff",
            100: "#dbeafe",
            200: "#bfdbfe",
            300: "#93c5fd",
            400: "#60a5fa",
            500: "#3b82f6", // Blue for App Store
            600: "#2563eb",
            700: "#1d4ed8",
            800: "#1e40af",
            900: "#1e3a8a",
            DEFAULT: "#3b82f6",
            foreground: "#ffffff",
          },
          danger: {
            50: "#fef2f2",
            100: "#fee2e2",
            200: "#fecaca",
            300: "#fca5a5",
            400: "#f87171",
            500: "#d62d20", // Huawei red
            600: "#dc2626",
            700: "#b91c1c",
            800: "#991b1b",
            900: "#7f1d1d",
            DEFAULT: "#d62d20",
            foreground: "#ffffff",
          },
        },
      },
      dark: {
        colors: {
          background: "#000000",
          foreground: "#ECEDEE",
          primary: {
            50: "#262626",
            100: "#404040",
            200: "#525252",
            300: "#737373",
            400: "#a3a3a3",
            500: "#ffffff", // Main white (inverted for dark)
            600: "#e5e5e5",
            700: "#d4d4d4",
            800: "#a3a3a3",
            900: "#737373",
            DEFAULT: "#ffffff",
            foreground: "#000000",
          },
          secondary: {
            50: "#450a0a",
            100: "#7f1d1d",
            200: "#991b1b",
            300: "#b91c1c",
            400: "#dc2626",
            500: "#ef4444", // Main red
            600: "#f87171",
            700: "#fca5a5",
            800: "#fecaca",
            900: "#fee2e2",
            DEFAULT: "#ef4444",
            foreground: "#ffffff",
          },
          warning: {
            50: "#451a03",
            100: "#78350f",
            200: "#92400e",
            300: "#b45309",
            400: "#d97706",
            500: "#f59e0b", // Yellowish accent
            600: "#fbbf24",
            700: "#fcd34d",
            800: "#fde68a",
            900: "#fef3c7",
            DEFAULT: "#f59e0b",
            foreground: "#000000",
          },
          success: {
            50: "#14532d",
            100: "#166534",
            200: "#15803d",
            300: "#16a34a",
            400: "#22c55e",
            500: "#22c55e", // Green for Play Store
            600: "#4ade80",
            700: "#86efac",
            800: "#bbf7d0",
            900: "#dcfce7",
            DEFAULT: "#22c55e",
            foreground: "#ffffff",
          },
          default: {
            50: "#1e3a8a",
            100: "#1e40af",
            200: "#1d4ed8",
            300: "#2563eb",
            400: "#3b82f6",
            500: "#3b82f6", // Blue for App Store
            600: "#60a5fa",
            700: "#93c5fd",
            800: "#bfdbfe",
            900: "#dbeafe",
            DEFAULT: "#3b82f6",
            foreground: "#ffffff",
          },
          danger: {
            50: "#7f1d1d",
            100: "#991b1b",
            200: "#b91c1c",
            300: "#dc2626",
            400: "#f87171",
            500: "#d62d20", // Huawei red
            600: "#fca5a5",
            700: "#fecaca",
            800: "#fee2e2",
            900: "#fef2f2",
            DEFAULT: "#d62d20",
            foreground: "#ffffff",
          },
        },
      },
    },
  })],
}
